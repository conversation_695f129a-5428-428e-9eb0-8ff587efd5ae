import fetchGraphQL from './fetchGraphQL'
import { extractMarketingModules } from './extractMarketingModules'

import { fetchVariantAvailability } from '../shopify/fetchAvailability'
import { getProductReviewsAggregate } from '../okendo'

import { removeSpaces, slugify } from '@utils/regex'
import { notEmpty } from '@utils/checking'
import RichTextRenderer from '@utils/RichTextRenderer'
import { generateVariantCombinations, SUPPORTED_ATTRIBUTES } from '@utils/urls'

import type {
  ProductFieldsProps,
  ProductDetailsProps,
  PageSectionsProps,
  ProductCollectionSlugResponse,
} from './types'
import type { ContentfulProductDetailsBlock } from './types/blocks'
import type { ContentfulProduct } from './types/products'

export type ProductProps = ProductFieldsProps & ProductDetailsProps & PageSectionsProps

// Extended type that includes runtime-added properties
export type EnhancedProductProps = ProductProps & {
  __typename?: 'Product';
  productType?: string;
  pageSectionsCollection?: any;
  productMetadata?: {
    items: Array<{
      type: string;
      references?: {
        items: Array<{
          slug?: string;
        }>;
      };
    }>;
  };
  variants: {
    items: Array<{
      sku: string;
      swatch: {
        slug: string;
        presentation: string;
        icon: { url: string };
        style: string;
        swatchType?: string; // Added in runtime
        swatchTypeLabel?: string; // Added in runtime
        linkedFrom: {
          swatchCollectionCollection: {
            items: Array<{
              linkedFrom: {
                swatchesCollection: {
                  items: Array<{ slug: string }>;
                };
              };
            }>;
          };
        };
      };
      variantId: number;
      price: number;
      compareAtPrice: number;
      primaryImage: { title: string; url: string };
      estimatedShippingDate?: string;
      variantAvailability?: string;
      availableForSale?: boolean; // Added in runtime
    }>;
  };
  groups?: {
    items: Array<{
      __typename?: string;
      sys?: { id: string };
      slug?: string;
    }>;
  };
  reviews?: {
    rating: number;
    count: number;
  };
}

const callToActionFragment = `
  fragment callToActionFragment on BlockCallToAction {
    name
    text
    page {
      __typename
      ... on Page {
        slug
      }
      ... on Product {
        slug
      }
      ... on BlogArticle {
        slug
      }
      ... on BlockDialog {
        __typename
        sys {
          id
        }
      }
    }
    icon {
      fileName
      url
    }
    customUrl
    anchor
    settings {
      theme
      layout
      fontFamily
    }
    sys {
      id
    }
  }
`

const blockContentFragment = `
  fragment blockContentFragment on BlockContent {
    __typename
    name
    title
    assetsCollection(limit: 10) {
      items {
        fileName
        url
        width
        height
      }
    }
    mobileAssetsCollection(limit: 1) {
      items {
        fileName
        url
      }
    }
    content {
      json
    }
    sys {
      id
    }
    settings {
      theme
      fontFamily
      customProps
      layout
    }
    referencesCollection(limit: 10) {
      items {
        __typename
        ... on BlockContent {
          name
          title
          content {
            json
          }
          sys {
            id
          }
          assetsCollection(limit: 4) {
            items {
              title,
              description,
              url
              fileName
              width
              height
            }
          }
          settings{
            theme
          }
        }
        ... on PageSectionsContent {
          name
          subtype
          header
          subheader
          content {
            json
          }
          assetsCollection(limit: 2) {
            items {
              fileName
              url
            }
          }
          blocksCollection(limit: 5) {
            items {
              __typename
              ... on BlockContent {
                name
                assetsCollection(limit: 2) {
                  items {
                    fileName
                    url
                  }
                }
                content {
                  json
                }
                sys {
                  id
                }
                settings {
                  fontFamily
                }
                referencesCollection(limit: 3) {
                  items {
                    __typename
                    ... on Product {
                      slug
                    }
                  }
                }
              }
              ... on BlockContentQuote {
                rating
                name
                author
                quote {
                  json
                }
                imagesCollection(limit: 2) {
                  items {
                    url
                  }
                }
                logo {
                  url
                }
              }
              ... on BlockCallToAction {
                name
                text
                page {
                  __typename
                  ... on Page {
                    slug
                  }
                  ... on Product {
                    slug
                  }
                  ... on BlogArticle {
                    slug
                  }
                  ... on BlockDialog {
                    sys {
                      id
                    }
                  }
                }
                icon {
                  fileName
                  url
                }
                anchor
                settings {
                  theme
                  layout
                }
                sys {
                  id
                }
              }
            }
          }
          settings {
            theme
            layout
          }
        }
        ...callToActionFragment
      }
    }
  }
`

export const blockCallToActionFragment = `
  fragment blockCallToActionFragment on BlockCallToAction {
    __typename
    name
    text
    page {
      __typename
      ... on Page {
        slug
      }
      ... on Product {
        slug
      }
      ... on BlogArticle {
        slug
      }
      ... on BlockDialog {
        sys {
          id
        }
      }
    }
    icon {
      fileName
      url
    }
    anchor
    settings {
      theme
      layout
    }
    sys {
      id
    }
  }
`

const blockContentQuoteFragment = `
  fragment blockContentQuoteFragment on BlockContentQuote {
    author
    quote {
      json
    }
    logo {
      title
      url
    }
    rating
  }
`

export const variantBasicInfoFragment = `
  fragment VariantBasicInfoFragment on Variant {
    __typename
    variantId
    name
    price
    sku
    primaryImage {
      url
    }
    estimatedShippingDate
    variantAvailability
  }
`

// TODO: extract this to a shared file
const SEO_METADATA_FIELDS = `
  seoMetadata {
    blockSearchIndexing
    keywords
    name
    description
    image {
      url
    }
    keywords
    canonicalUrl {
      ... on Page {
        slug
      }
      ... on Product {
        slug
      }
    }
    blockSearchIndexing
  }
`

const DEFAULT_NUMBER_OF_DETAIL_BLOCKS = 10
const productDetailsFields = (limit = DEFAULT_NUMBER_OF_DETAIL_BLOCKS) => `
  __typename
  items {
    type
    title
    description {
      json
    }
    contentCollection(limit: ${limit}) {
      items {
        __typename
        ... on BlockContent {
          title
          content {
            json
          }
          settings {
            theme
          }
          sys {
            id
          }
          assetsCollection(limit: 10) {
            items {
              fileName
              url
              description
            }
          }
          referencesCollection(limit: 4) {
            ...on BlockContentReferencesCollection {
              items {
                ...blockCallToActionFragment
                ...on BlockContent {
                  name
                  title
                  content {
                    json
                  }
                  assetsCollection(limit: 2) {
                    items {
                      fileName
                      url
                      description
                    }
                  }
                  settings {
                    theme
                  }
                  referencesCollection(limit: 13) {
                    ...on BlockContentReferencesCollection {
                      items {
                        ...on BlockContent {
                          name
                          title
                          content {
                            json
                          }
                        }
                      }
                    }
                  }
                }
                ...VariantBasicInfoFragment
                ... on Variant {
                  swatch {
                    __typename
                    presentation
                    slug
                    style
                    icon {
                      url
                    }
                  }
                }
              }
            }
          }
        }
        ... blockCallToActionFragment
        ... on Variant {
          ...VariantBasicInfoFragment
          linkedFromProduct: linkedFrom {
            productCollection(limit: 1) {
              items {
                title
                slug
                productId
                linkedFrom {
                  collectionCollection(limit: 5) {
                    items {
                      name
                    }
                  }
                }
                seoMetadata {
                  description
                }
                productDetailBlocksCollection(limit: 1, where: {type: "Product Byline"}) {
                  items {
                    type
                    description {
                      json
                    }
                  }
                }
              }
            }
          }
          swatch {
            __typename
            presentation
            slug
            style
            icon {
              url
            }
          }
          productDetailBlocksOverrides: productDetailBlocksOverridesCollection(limit: 1, where: {type: "Product Byline"}) {
            __typename
            items {
              type
              description {
                json
              }
            }
          }
        }
        ... on Product {
          title
          slug
          productId
          variants: variantsCollection(limit: 10) {
            items {
              sys {
                id
              }
              sku
              swatch {
                slug
                presentation
                icon {
                  url
                }
                style
              }
              variantId
              price
              compareAtPrice
              primaryImage {
                title
                url(transform: {
                  quality: 75
                })
              }
              estimatedShippingDate
              variantAvailability
              productDetailBlocksOverrides: productDetailBlocksOverridesCollection(limit: 1) {
                items {
                  type
                  title
                  description {
                    json
                  }
                }
              }
            }
          }
          seoMetadata {
            description
          }
          productDetailBlocksCollection(limit: 1, where: {type: "Product Byline"}) {
            items {
              type
              description {
                json
              }
            }
          }
        }
      }
    }
  }
`

const PRODUCT_DETAILS_QUERY = `
  productDetailBlocksCollection(limit: 10) {
    ${productDetailsFields()}
  }
`

export const PRODUCT_FIELDS_QUERY = `
  __typename
  slug
  title
  productId
  seoMetadata {
    name
    description
    image {
      url
    }
  }
  linkedFrom {
    collectionCollection(limit: 5) {
      items {
        name
      }
    }
  }
  productMetadata: productMetadataCollection(limit: 5, where: {type_not: "Quantities"}) {
    items {
      type
      media: mediaCollection(limit: 5) {
        items {
          url
        }
      }
      description {
        json
      }
      references: referencesCollection(limit: 7) {
        items {
          ...on BlockDialog {
            sys { id }
            header
            subheader
            content { json }
            mobileContent { json }
            footerContent { json }
            asset { url }
            settings {
              theme
              layout
            }
            sys { id }
            dialogTogglesCollection(limit: 1) {
              items {
                __typename
                ... on PageSectionsContent {
                  sectionType: __typename
                  subtype
                  sys { id } 
                }
               ...on CompareChart {
                  __typename
                  header
                  elementsToCompareCollection(limit: 5) {
                    items {
                      ...on CompareItems {
                        name
                        description
                        selectedAsset {
                          url
                          description
                        }
                        itemsCollection(limit:2) {
                          items {
                            title
                            content {
                              json
                            }
                            assetsCollection(limit: 1) {
                              items {
                                description
                                url
                              }
                            }
                            mobileAssetsCollection(limit: 1) {
                              items {
                                url
                                description
                              }
                            }
                          }
                        }
                      }
                      callToAction {
                        text
                        page {
                          __typename
                          ... on Page {
                            slug
                          }
                          ... on Product {
                            slug
                          }
                          ... on BlogArticle {
                            slug
                          }
                          ... on BlockDialog {
                            sys {
                              id
                            }
                          }
                        }
                      }
                    }
                  }
                }
                ...on BlockContent {
                  __typename
                  title
                  content {
                    json
                  }
                  mobileContent {
                    json
                  }
                  assetsCollection(limit:1) {
                    ...on AssetCollection {
                      items {
                        fileName
                        url
                      }
                    }
                  }
                }
              }
            }
          }
          ... on Swatch {
            slug
          }
        }
      }
    }
  }
  quantities: productMetadataCollection(limit:1, where:{ type: "Quantities"}){
    items {
      name
      toggles: referencesCollection(limit:4) {
        items {
          ... on QuantityToggle {
            name
            presentation
            slug
            quantity
            calloutsCollection(limit: 1) {
              items {
                title
                settings {
                  theme
                }
              sys {
                id
                }
              }
            }
          }
        }
      }
    }
  }
  swatches: swatchesCollection(limit: 3) {
    items {
      __typename
      slug
      presentation
      swatchCollections: swatchCollectionsCollection(limit: 3) {
        items {
          __typename
          slug
          presentation
          swatches: swatchesCollection(limit: 10) {
            __typename
            items {
              slug
              presentation
              icon {
                url
              }
              style
            }
          }
        }
      }
    }
  }
  variants: variantsCollection(limit: 15) {
    items {
      gtin8: globalTradeItemNumber
      sku
      swatch {
        __typename
        slug
        presentation
        icon {
          url
        }
        style
        linkedFrom {
          swatchCollectionCollection(limit: 1) {
            items {
              linkedFrom {
                swatchesCollection(limit: 1) {
                  items {
                    slug
                    presentation
                  }
                }
              }
            }
          }
        }
      }
      swatches: swatchesCollection(limit: 3) {
        items {  
          slug
        }
      }
      variantId
      price
      compareAtPrice
      regularPriceOverride
      onSale
      primaryImage {
        title
        url(transform: {
          quality: 75
        })
      }
      estimatedShippingDate
      variantAvailability
      gallery: galleryCollection(limit: 10) {
        items {
          title
          description
          contentType
          url
          width
          height
        }
      }
      productDetailBlocksOverrides: productDetailBlocksOverridesCollection(limit: 1) {
        ${productDetailsFields(1)}
      }
    }
  }
  callouts: calloutsCollection(limit: 5) {
    items {
      title
      settings {
        theme
        layout
      }
      badge {
        title
        description
        contentType
        url
        width
        height
      }
      placement
      sys {
        id
      }
    }
  }
  groups: groupsCollection(limit: 5) {
    items {
      ... on CompareSet {
        __typename
        sys {
          id
        }
      }
      ... on ProductsGroup {
        __typename
        sys {
          id
        }
      }
    }
  }
  compare {
    title
    description
    callout {
     title
      settings {
        theme
      }
      sys {
        id
      }
    }
    selectedAsset {
      width
      height
      title
      url
    }
    notSelectedAsset {
      width
      height
      title
      url
    }
    itemsCollection(limit: 5) {
      items {
        sys { id }
        title
        content {
          json
        }
        assetsCollection(limit: 1){
          items{
            url
            width
            height
            title
          }
        }
      }
    }
  }
`

const COMPARE_SET_FIELDS = `
  __typename
  openDialog {
    text
    page {
      ...on BlockDialog {
        sys {
          id
        }
      }
    }
  }
`

const PRODUCT_GROUP_FIELDS = `
  __typename
  name
  slug
  settings {
    layout
  }
  products: productsCollection(limit: 5) {
    items {
      title
      slug
      productId
      variants: variantsCollection(limit: 15) {
        items {
          sku
          swatch {
            __typename
            slug
            presentation
            icon {
              url
            }
            style
            linkedFrom {
              swatchCollectionCollection(limit: 1) {
                items {
                  linkedFrom {
                    swatchesCollection(limit: 1) {
                      items {
                        slug
                        presentation
                      }
                    }
                  }
                }
              }
            }
          }
          swatches: swatchesCollection(limit: 3) {
            items {  
              slug
            }
          }
          variantId
          price
          compareAtPrice
          primaryImage {
            title
            url(transform: {
              quality: 75
            })
          }
        }
      }
      groups: groupsCollection(limit: 5) {
        items {
          ...on ProductsGroup {
            name
            slug
            groupItem: productsGroupItemsCollection(limit: 5) {
              __typename
              items {
                title
                subheader
                callout: callout {
                  title
                  sys {
                    id
                  }
                  settings {
                    theme
                  }
                }
                product: product{
                  title
                  slug
                  productId
                }
                referencesCollection(limit: 5) {
                  items {
                    ...on BlockContent {
                      title
                      settings {
                        theme
                      }
                    }
                  }
                }
              }
            }
            settings {
              layout
            }
          }
        }
      }
    }
  }
`

const PAGE_SECTIONS_QUERY = `
  pageSectionsCollection(limit: 10) {
    sections: items {
      sectionType: __typename
      ... on PageSectionsReadyToUse {
        name
        subtype
      }
      ... on PageSectionsContent {
        name
        subtype
        header
        subheader
        content {
          json
        }
        assetsCollection(limit: 10) {
          items {
            fileName
            url
          }
        }
        mobileAssetsCollection(limit: 2) {
          items {
            fileName
            url
          }
        }
        blocksCollection(limit: 20) {
          items {
            ... on BlockContent {
              __typename
              sys { id }
            }
            ... on BlockCallToAction {
              __typename
              sys { id }
            }
            ... on BlockContentQuote {
              __typename
              sys { id }
            }
          }
        }
        settings {
          theme
          layout
          anchorTargeting
        }
      }
      ... on PageSectionsProduct {
        title
        subtype
        productsCollection(limit: 10) {
          items {
            __typename
            ... on Product {
              slug
            }
            ... on ProductCard {
              sys {
                id
              }
            }
            ... on BlockContent {
              sys {
                id
              }
            }
            ... on Collection {
              name
              productsCollection(limit: 16) {
                items {
                  __typename
                  ... on Product {
                    slug
                  }
                  ... on ProductCard {
                    sys {
                      id
                    }
                  }
                }
              }
            }
          }
        }
        settings {
          anchorTargeting
        }
      }
    }
  }
`

export const PRODUCT_FIELDS = `
  __typename
  slug
  title
  seoMetadata {
    keywords
    name
    description
    image {
      url
    }
    keywords
    canonicalUrl {
      ... on Page {
        slug
      }
      ... on Product {
        slug
      }
    }
    blockSearchIndexing
  }
  productId
  productMetadata: productMetadataCollection(limit: 5) {
    items {
      type
      media: mediaCollection(limit: 5) {
        items {
          url
        }
      }
      description {
        json
      }
    }
  }
  swatches: swatchesCollection(limit: 3) {
    items {
      slug
    }
  }
  variants: variantsCollection(limit: 10) {
    items {
      sku
      swatch {
        slug
        presentation
        icon {
          url
        }
        style
        linkedFrom {
          swatchCollectionCollection(limit: 1) {
            items {
              linkedFrom {
                swatchesCollection(limit: 1) {
                  items {
                    slug
                  }
                }
              }
            }
          }
        }
      }
      variantId
      price
      compareAtPrice
      primaryImage {
        title
        url(transform: {
          quality: 75
        })
      }
      productDetailBlocksOverrides: productDetailBlocksOverridesCollection(limit: 10) {
        sys {
          id
        }
      }
    }
  }
  callouts: calloutsCollection(limit: 5) {
    items {
      title
      settings {
        theme
        layout
      }
      sys {
        id
      }
    }
  }
  productDetailBlocksCollection(limit: 10) {
    items {
      type
      description {
        json
      }
    }
  }
`

const extractProductSlugs = (fetchResponse: ProductCollectionSlugResponse) => fetchResponse.data.productCollection.items.map((product) => ({
  slug: product.slug,
}))

export const extractProductEntries = (fetchResponse: { data: { productCollection: { items: any[] } } }) => fetchResponse.data.productCollection.items[0]

export async function getProductFields(slug: string) {
  const query = `
  query {
      productCollection(where: {slug: "${slug}"}, limit: 1) {
        items {
          ${PRODUCT_FIELDS_QUERY}
          ${SEO_METADATA_FIELDS}
        }
      }
    }
    ${variantBasicInfoFragment}
    ${blockCallToActionFragment}
  `

  const response = await fetchGraphQL(
    query,
    ['productFields']
  )
  return extractProductEntries(response)
}

export async function getAllProductsWithVariantCustomMessage() {
  const query = `
  query {
    productCollection(
      where: {
        productMetadataCollection_exists: true
      }
    ){
      items{
        title
        sys{
          id
        }
        slug
        productMetadataCollection(where: { type_contains: "Variant Custom Message" }){
          items {
            type
            media: mediaCollection(limit: 5) {
              items {
                url
              }
            }
            description {
              json
            }
            references: referencesCollection(limit: 5) {
              items {
                ... on Swatch {
                  slug
                }
              }
            }
          }
        }
      }
    }
  }
  `

  const response = await fetchGraphQL(
    query,
    ['productFields']
  )

  const { items } = response.data.productCollection
  const filteredItems = items.filter((item: any) => item.productMetadataCollection.items.length > 0) || []

  return filteredItems
}

export async function getProductDetails(slug: string) {
  const query = `
    query {
      productCollection(where: {slug: "${slug}"}, limit: 1) {
        items {
          ${PRODUCT_DETAILS_QUERY}
        }
      }
    }
    ${variantBasicInfoFragment}
    ${blockCallToActionFragment}
  `

  const response = await fetchGraphQL(
    query,
    ['productDetails']
  )
  return extractProductEntries(response)
}

export const extractPageSections = async (fetchResponse: {
  data: { productCollection: { items: any[] } };
}) => {
  // Check if the product exists and has pageSectionsCollection
  const product = fetchResponse.data.productCollection.items[0]

  if (!product || !product.pageSectionsCollection || !product.pageSectionsCollection.sections) {
    console.warn('Product or pageSectionsCollection not found in extractPageSections')
    return { pageSectionsCollection: { sections: [] } }
  }

  const sections = product.pageSectionsCollection.sections.map(async (section: any) => {
    if (section.sectionType === 'PageSectionsContent') {
      const blocks = section.blocksCollection.items
        .map(async (block: any) => {
          if (block.__typename === 'BlockContent') {
            const response = await fetchGraphQL(
              `
                query {
                  blockContent(id: "${block.sys.id}") {
                    ...blockContentFragment
                  }
                }
                ${blockContentFragment}
                ${callToActionFragment}
              `,
              [`blockContent-${block.sys.id}`]
            )

            return response.data.blockContent
          }

          if (block.__typename === 'BlockCallToAction') {
            const response = await fetchGraphQL(
              `
                query {
                  blockCallToAction(id: "${block.sys.id}") {
                    ...blockCallToActionFragment
                  }
                }
                ${blockCallToActionFragment}
              `,
              [`blockCallToAction-${block.sys.id}`]
            )

            return response.data.blockCallToAction
          }

          if (block.__typename === 'BlockContentQuote') {
            const response = await fetchGraphQL(
              `
                query {
                  blockContentQuote(id: "${block.sys.id}") {
                    ...blockContentQuoteFragment
                  }
                }
                ${blockContentQuoteFragment}
              `,
              [`blockContentQuote-${block.sys.id}`]
            )

            return response.data.blockContentQuote
          }

          return null
        })
        .filter(Boolean)

      return {
        ...section,
        blocksCollection: {
          items: await Promise.all(blocks)
        }
      }
    }

    return section
  })

  const data = fetchResponse.data.productCollection.items[0]

  data.pageSectionsCollection.sections = await Promise.all(sections)

  return data
}

export async function getPageSections(slug: string) {
  try {
    const query = `
      query {
        productCollection(where: {slug: "${slug}"}, limit: 1) {
          items {
            ${PAGE_SECTIONS_QUERY}
          }
        }
      }
    `

    const response = await fetchGraphQL(
      query,
      ['pageSections']
    )

    // Check if product exists
    if (!response.data?.productCollection?.items?.[0]) {
      return { pageSectionsCollection: { sections: [] } }
    }

    return await extractPageSections(response)
  } catch (error) {
    return { pageSectionsCollection: { sections: [] } }
  }
}

export async function getAllProductSlugs() {
  const response = await fetchGraphQL(
    `
      query {
        productCollection(limit: 200) {
          items {
            slug
          }
        }
      }
    `,
    ['products']
  )
  return extractProductSlugs(response)
}

/**
 * Get all variant slugs for all products
 * @param productSlugs Array of product slug objects
 * @param fetchProduct Function to fetch a product by slug
 * @returns Array of static parameters for all products and their variants
 */
export async function getAllVariantsSlugs(
  productSlugs: { slug: string }[],
  fetchProduct: (slug: string) => Promise<any>
): Promise<Array<{ slug: string, variants: string }>> {
  // First, collect all group slugs from all products
  const allGroupSlugs = new Set<string>()

  // Fetch all products to collect group information
  const allProducts = await Promise.all(
    productSlugs.map(async ({ slug }) => {
      const product = await fetchProduct(slug)

      // Collect group slugs from this product
      if (product?.groups?.items?.length > 0) {
        product.groups.items.forEach((group: any) => {
          if (group.__typename === 'ProductsGroup' && group.slug) {
            allGroupSlugs.add(group.slug)
          }
        })
      }

      return { slug, product }
    })
  )

  // Prepare promises for parallel processing
  const staticParamsPromises = allProducts.map(async ({ slug, product }) => {
    const params: { slug: string, variants: string }[] = []

    if (product?.variants?.items?.length) {
      // First, generate single variant paths
      const singleVariantPaths = new Set<string>()

      // Add individual variant attributes from the product itself
      product.variants.items.forEach((variant: any) => {
        if (variant.swatch) {
          const { swatchType, slug: swatchSlug } = variant.swatch
          singleVariantPaths.add(`${swatchType}=${swatchSlug}`)
        }

        // Handle multi-attribute variants
        if (variant.swatches?.items?.length > 0) {
          variant.swatches.items.forEach((swatch: any) => {
            const swatchType = swatch.swatchType || swatch.slug
            const swatchValue = swatch.slug
            singleVariantPaths.add(`${swatchType}=${swatchValue}`)
          })
        }
      })

      // Collect variants from all products in the same groups
      const groupVariants: any[] = []
      if (product?.groups?.items?.length > 0) {
        product.groups.items.forEach((group: any) => {
          if (group.__typename === 'ProductsGroup') {
            // Add the group itself as a variant attribute
            singleVariantPaths.add(`group=${group.slug}`)

            // Collect variants from all products in this group
            if (group.products?.items?.length > 0) {
              group.products.items.forEach((groupProduct: any) => {
                if (groupProduct.variants?.items?.length > 0) {
                  groupProduct.variants.items.forEach((variant: any) => {
                    // Process swatches with proper swatchType extraction
                    if (variant.swatch?.linkedFrom?.swatchCollectionCollection?.items?.[0]?.linkedFrom?.swatchesCollection?.items?.[0]) {
                      const swatchTypeInfo = variant.swatch.linkedFrom.swatchCollectionCollection.items[0].linkedFrom.swatchesCollection.items[0]
                      const swatchType = swatchTypeInfo.slug
                      const swatchSlug = variant.swatch.slug

                      // Add individual variant from group products
                      singleVariantPaths.add(`${swatchType}=${swatchSlug}`)

                      // Store for combination generation
                      groupVariants.push({
                        swatch: {
                          swatchType,
                          slug: swatchSlug
                        },
                        swatches: variant.swatches
                      })
                    }
                  })
                }
              })
            }
          }
        })
      }

      // Add individual quantity attributes if they exist
      if (product?.quantities?.items?.length > 0) {
        const quantityToggles = product.quantities.items[0]?.toggles?.items || []
        quantityToggles.forEach((toggle: any) => {
          singleVariantPaths.add(`quantity=${toggle.slug}`)
        })
      }

      // Add single variant paths to params
      singleVariantPaths.forEach((variantPath) => {
        params.push({
          slug,
          variants: variantPath
        })
      })

      // Generate variant combinations using both product variants and group variants
      const allVariantsForCombinations = [
        ...product.variants.items,
        ...groupVariants
      ]

      const variantCombinations = generateVariantCombinations(allVariantsForCombinations, product)

      // Create static paths for each variant combination (excluding single variants already added)
      variantCombinations.forEach((variantCombo) => {
        // Only combinations with multiple attributes
        if (Object.keys(variantCombo).length > 1) {
          // Sort parameters in the same order as SUPPORTED_ATTRIBUTES to ensure consistency
          const variantPath = SUPPORTED_ATTRIBUTES
            .filter((attr: string) => variantCombo[attr as keyof typeof variantCombo])
            .map((attr: string) => `${attr}=${slugify(variantCombo[attr as keyof typeof variantCombo]!)}`)
            .join('&')

          params.push({
            slug,
            variants: variantPath
          })
        }
      })

      // IMPORTANT: Generate combinations of this product's variants with ALL group slugs
      // This ensures that products can be accessed with group parameters from any group
      if (product.variants?.items?.length > 0 && allGroupSlugs.size > 0) {
        allGroupSlugs.forEach((groupSlug) => {
          product.variants.items.forEach((variant: any) => {
            if (variant.swatch?.linkedFrom?.swatchCollectionCollection?.items?.[0]?.linkedFrom?.swatchesCollection?.items?.[0]) {
              const swatchTypeInfo = variant.swatch.linkedFrom.swatchCollectionCollection.items[0].linkedFrom.swatchesCollection.items[0]
              const swatchType = swatchTypeInfo.slug
              const swatchSlug = variant.swatch.slug

              // Generate combination of this product's variant with the group
              const combinedPath = `${swatchType}=${slugify(swatchSlug)}&group=${slugify(groupSlug)}`
              params.push({
                slug,
                variants: combinedPath
              })
            }
          })

          // Also generate group-only paths (without specific variant)
          params.push({
            slug,
            variants: `group=${slugify(groupSlug)}`
          })
        })
      }
    }

    return params
  })

  // Resolve all promises and flatten the array
  const nestedParams = await Promise.all(staticParamsPromises)
  return nestedParams.flat()
}

export async function getBlockContentByID(id: string, productBlockContent = false) {
  const response = await fetchGraphQL(
    `
      query {
        blockContent(id: "${id}") {
          __typename
          sys {
            id
          }
          title
          content {
            json
          }
          assetsCollection(limit: 4) {
            items {
              title
              url
            }
          }
          mobileAssetsCollection(limit: 4) {
            items {
              title
              url
            }
          }
          referencesCollection(limit: 4) {
            ...on BlockContentReferencesCollection {
              items {
                __typename
                ...on BlockContent {
                  title
                  content {
                    json
                  }
                  assetsCollection(limit: 4) {
                    items {
                      title
                      url
                      height
                      width
                    }
                  }
                }
                ... on BlockContentQuote {
                  author
                  quote {
                    json
                  }
                  logo {
                    title
                    url
                  }
                  rating
                }
                ...on Product {
                  slug
                }
                ...on BlockCallToAction {
                  name
                  text
                  settings {
                    theme
                  }
                  page {
                    __typename
                    ... on Page {
                      slug
                    }
                    ... on Product {
                      slug
                    }
                    ... on BlogArticle {
                      slug
                    }
                    ... on BlockDialog {
                      sys {
                        id
                      }
                    }
                  }
                }
              }
            }
          }
          settings {
            layout
            theme
          }
        }
      }
    `
  )
  if (productBlockContent) return response.data.blockContent
  return extractMarketingModules(response.data.blockContent)
}

// TODO: Specify only required fields for performance
const extractProductCardEntries = (fetchResponse: { data: { productCard: any } }) => fetchResponse.data.productCard

export async function getProductCardByID(id: string, forceCache = false) {
  const response = await fetchGraphQL(
    `
      query {
        productCard(id: "${id}") {
          __typename
          size
          hoverImage {
            url
          }
          gallery: galleryCollection(limit: 4) {
            items {
              title
              contentType
              url
            }
          }
          product {
            ${PRODUCT_FIELDS_QUERY}
          }
          variantCards: variantCardsCollection(limit: 10) {
            items {
              ... on VariantCard {
                __typename
                image {
                  contentType
                  title
                  url
                }
                swatch {
                  slug
                }
              }
              ... on Swatch {
                __typename
                slug
              }
            }
          }
          description {
            json
          }
        }
      }
      ${variantBasicInfoFragment}
      ${blockCallToActionFragment}
    `,
    ['productCard'],
    false,
    forceCache
  )
  return extractProductCardEntries(response)
}

export const extractProductDetails = async (product: ContentfulProduct) => {
  const { productDetailBlocksCollection: { items } } = product

  const details = items.map((item) => ({
    __typename: item.__typename,
    // TODO: we proabely want to make this PascalCase (see AddtoCartButton for example)
    type: removeSpaces(item.type).replace('-', '') as ContentfulProductDetailsBlock['type'],
    title: item.title,
    description: item.description ? item.description : undefined,
    contentCollection: item.contentCollection,
    blocks: item?.contentCollection?.items.filter((block) => block.__typename === 'BlockContent').map((block) => ({
      typename: block.__typename,
      title: block.title,
      content: block.content && RichTextRenderer({ content: block.content.json }),
      contentRaw: block.content,
      id: block.sys.id,
      settings: block.settings,
      assets: block.assetsCollection?.items ? block.assetsCollection.items.map((asset) => ({
        fileName: asset.fileName,
        url: asset.url,
        description: asset.description
      })) : null,
      variants: block?.referencesCollection ? block.referencesCollection.items.filter((ref) => ref.__typename === 'Variant').map((variant) => ({
        id: variant.variantId,
        price: variant.price,
        variant,
        product: variant.linkedFromProduct?.productCollection?.items[0]
      })) : null,
      referencesCollection: block?.referencesCollection ? block.referencesCollection.items.map((reference: any) => ({
        content: reference.content,
        name: reference.name,
        reference: reference.title,
        callToAction: reference.__typename === 'BlockCallToAction' ? reference : null,
        asset: reference.assetsCollection?.items ? reference.assetsCollection.items.map((asset: any) => ({
          fileName: asset.fileName,
          url: asset.url,
          description: asset.description
        })) : null,
        referencesCollection: reference?.referencesCollection ? reference.referencesCollection.items.map((secondaryRef: any) => ({
          content: secondaryRef.content,
          name: secondaryRef.name,
          title: secondaryRef.title,
          items: secondaryRef?.referencesCollection ? secondaryRef.referencesCollection.items.map((tertiaryRef: any) => ({
            content: tertiaryRef.content,
            title: tertiaryRef.title
          })) : null
        })) : null
      })) : null
    })),
    variants: item?.contentCollection?.items.filter((block) => block.__typename === 'Variant').map((variant) => {
      const variantProduct = variant?.linkedFromProduct?.productCollection?.items.map((_product) => ({
        slug: _product.slug,
        title: _product.title,
        byline: _product.productDetailBlocksCollection?.items?.[0]
      }))[0]

      const overrides = variant.productDetailBlocksOverrides?.items.map((override) => ({
        type: override.type,
        description: override.description
      })) || []

      const productByline = variantProduct?.byline
      const variantByline = overrides[0]

      return {
        __typename: variant.__typename,
        id: variant.variantId,
        name: variantProduct?.title || 'Product',
        price: variant.price,
        image: variant.primaryImage?.url || '',
        description: variantByline?.description || productByline?.description,
        variant,
        product: variant.linkedFromProduct?.productCollection?.items[0],
        estimatedShippingDate: variant.estimatedShippingDate,
      }
    }),
    products: item?.contentCollection?.items.filter((block) => block.__typename === 'Product').map((_product) => {
      const variants = _product.variants?.items?.map((variant) => {
        const overrides = variant.productDetailBlocksOverrides?.items.map((override) => ({
          type: override.type,
          description: override.description
        })) || []

        const productByline = _product.productDetailBlocksCollection?.items?.[0]
        const variantByline = overrides[0]

        return {
          ...variant,
          description: variantByline?.description || productByline?.description,
        }
      }) || []

      return {
        ..._product,
        variants: { items: variants }
      }
    })
  }))

  await Promise.all(items)
  return details
}

type ExtractedProductDetails = Awaited<ReturnType<typeof extractProductDetails>>
type Blocks = ExtractedProductDetails[0]['blocks']
type Variants = ExtractedProductDetails[0]['variants']
type Products = ExtractedProductDetails[0]['products']
export type ContentfulExtractedProductDetailBlock = ContentfulProductDetailsBlock & {
  blocks: Blocks;
  variants: Variants;
  products: Products;
};
export type ContentfulProductDetailBlocks = ContentfulExtractedProductDetailBlock[];
export type ContentfulProductDetailBlockData = NonNullable<Blocks>[0]

export async function getProductFieldsBySlug(slug: string, withDetails = false, forceCache = false) {
  const response = await fetchGraphQL(
    `
      query {
        productCollection(where: {slug: "${slug}"}, limit: 1) {
          items {
            ${PRODUCT_FIELDS_QUERY}
            ${withDetails ? PRODUCT_DETAILS_QUERY : ''}
          }
        }
      }
      ${variantBasicInfoFragment}
      ${blockCallToActionFragment}
    `,
    ['productFields'],
    false,
    forceCache
  )

  const product = extractProductEntries(response)
  const details = await extractProductDetails(product)
  const variants = product.variants.items.map(async (variant: any) => ({
    ...variant,
    availableForSale: await fetchVariantAvailability(variant.variantId),
  }))

  return {
    ...product,
    variants: { items: await Promise.all(variants) },
    productDetailBlocks: {
      items: details
    }
  }
}

async function getProductGroup(id: string) {
  const response = await fetchGraphQL(
    `
      query {
        productsGroup(id: "${id}") {
          ${PRODUCT_GROUP_FIELDS}
        }
      }
    `,
    [`productGroup-${id}`]
  )

  return response.data.productsGroup
}

async function getCompareSet(id: string) {
  const response = await fetchGraphQL(
    `
      query {
        compareSet(id: "${id}") {
          ${COMPARE_SET_FIELDS}
        }
      }
    `,
    [`compareSet-${id}`]
  )

  return response.data.compareSet
}

export async function getProductBySlug(slug: string): Promise<EnhancedProductProps | null> {
  const [productFields, productDetails, pageSections] = await Promise.all([
    getProductFields(slug),
    getProductDetails(slug),
    getPageSections(slug)
  ])

  if (!productFields || !productDetails || !pageSections) {
    return null
  }

  const { pageSectionsCollection: { sections } } = pageSections

  const data: ProductProps = {
    ...productFields as ProductFieldsProps,
    ...productDetails as ProductDetailsProps,
    sections
  }

  // TODO: rush delivery need to update types
  if (notEmpty(data)) {
    const variants = data.variants.items
    variants.forEach((item: any) => {
      const { swatch } = item
      const { linkedFrom: { swatchCollectionCollection: { items: swatchItems } } } = swatch
      const { linkedFrom: { swatchesCollection: { items: swatchesItems } } } = swatchItems[0]
      const { slug: swatchSlug, presentation } = swatchesItems[0]
      item.swatch.swatchType = swatchSlug
      item.swatch.swatchTypeLabel = presentation
    })

    data.variants.items = await Promise.all(
      variants.map(async (variant: any) => {
        variant.availableForSale = await fetchVariantAvailability(
          variant.variantId
        )
        return variant
      })
    )

    const groupIds = data.groups.items
    const groups = groupIds.map(async (group: any) => {
      if (group.__typename === 'CompareSet') {
        return getCompareSet(group.sys.id)
      }

      return getProductGroup(group.sys.id)
    })

    data.groups.items = await Promise.all(groups)

    data.reviews = await getProductReviewsAggregate(data.productId)
    return data
  }

  return null
}

export async function getProductID(
  slug: string,
) {
  const product = await fetchGraphQL(
    `query {
      productCollection(where: {slug: "${slug}"}, limit: 1) {
        items {
          productId
        }
      }
    }`,
    ['productID']
  )
  return extractProductEntries(product)
}

// TODO: Specify only required fields for performance
export async function getAllProducts() {
  const PRODUCT_LIMIT = 200
  const graphQlResponse = await fetchGraphQL(
    `
      query {
        productCollection(limit: ${PRODUCT_LIMIT}) {
          items {
            ${PRODUCT_FIELDS}
          }
        }
      }
    `,
    ['products']
  )

  const products = extractProductEntries(graphQlResponse)

  return products.map((product: any) => ({
    ...product,
    reviews: getProductReviewsAggregate(product.productId)
  }))
}

export async function getAllProductsWithMetadata() {
  const graphQlResponse = await fetchGraphQL(`
    query getIncludedItems {
      productCollection {
        items {
          name
          productId
          productMetadataCollection(limit: 5) {
            items {
              type
            }
          }
        }
      }
    }
  `)

  // TODO: needs to be typed
  const productsWithAnIncludedItem = graphQlResponse.data.productCollection.items.filter(
    (product: any) => !!product.productMetadataCollection.items.find(
      (item: any) => item.type === 'Included Item'
    )
  )

  return productsWithAnIncludedItem
}
